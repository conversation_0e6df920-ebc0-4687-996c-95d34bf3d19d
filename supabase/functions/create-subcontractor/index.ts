// supabase/functions/create-subcontractor/index.ts

import { SubcontractorService } from "./services/subcontractor.service.ts";
import { createSubcontractor<PERSON>oh<PERSON> } from "./services/create-subcontractor-zoho.ts";

// Helper function to convert files to email attachments
async function convertFilesToAttachments(files: { [key: string]: File }) {
  const attachments: Array<{
    content: string;
    filename: string;
    type: string;
    disposition: string;
  }> = [];

  for (const [fieldName, file] of Object.entries(files)) {
    if (file && file.size > 0) {
      // Convert file to base64
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const base64String = btoa(String.fromCharCode(...uint8Array));

      attachments.push({
        content: base64String,
        filename: file.name || `${fieldName}.${getFileExtension(file.type)}`,
        type: file.type || 'application/octet-stream',
        disposition: 'attachment'
      });
    }
  }

  return attachments;
}

// Helper function to get file extension from MIME type
function getFileExtension(mimeType: string): string {
  const mimeToExt: { [key: string]: string } = {
    'application/pdf': 'pdf',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'text/plain': 'txt'
  };

  return mimeToExt[mimeType] || 'bin';
}

type SubcontractorRequest = {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  addressLine1: string;
  addressLine2?: string;
  postCode: string;
  dateOfBirth: string;
  gasRegistered: boolean;
  yearsExperience: number;
  travelDistance: number;
  hasOwnVan: boolean;
  hasOwnTools: boolean;
  workType: string;
  centralLondon: boolean;
  drivingLicense: boolean;
  publicLiabilityInsurance: boolean;
  availableDays?: string[];
  acceptedRates: boolean;
  outOfHoursWork: boolean;
  emergencyCallouts: boolean;
  preferredWorkType: string;
  additionalQualifications?: string;
  // File attachments
  files?: {
    cv?: File;
    gasRegistrationCertificate?: File;
    publicLiabilityInsuranceCertificate?: File;
    drivingLicense?: File;
    additionalCertificates?: File[];
  };
};

async function handleCreateSubcontractor(request: Request): Promise<Response> {
  try {
    // Debug: Check environment variables
    // @ts-ignore: Deno is available in Supabase Edge Functions

    let body: SubcontractorRequest;
    let files: { [key: string]: File } = {};

    // Check content type and parse accordingly
    const contentType = request.headers.get("content-type") || "";

    if (contentType.includes("multipart/form-data")) {
      // Handle multipart form data (with files)
      const formData = await request.formData();

      // Parse form fields
      const formFields: any = {};
      const fileFields: { [key: string]: File } = {};

      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          fileFields[key] = value;
        } else {
          // Handle arrays (like availableDays)
          if (key === "availableDays") {
            formFields[key] = value.split(",").map(day => day.trim());
          } else if (value === "true" || value === "false") {
            formFields[key] = value === "true";
          } else if (!isNaN(Number(value)) && value !== "") {
            formFields[key] = Number(value);
          } else {
            formFields[key] = value;
          }
        }
      }

      body = formFields as SubcontractorRequest;
      files = fileFields;

      console.log("Received files:", Object.keys(files));
      console.log("Form data:", body);
    } else {
      // Handle JSON data (no files)
      body = await request.json();
    }

    // Validate required fields
    const requiredFields: (keyof SubcontractorRequest)[] = ["firstName", "lastName", "email", "mobile"];
    for (const field of requiredFields) {
      if (!body[field]) {
        return new Response(
          JSON.stringify({
            error: {
              message: `Missing required field: ${field}`,
            },
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          }
        );
      }
    }

    // Create subcontractor service instance
    const subcontractorService = new SubcontractorService();

    // Send to Zoho CRM
    const zohoResult = await createSubcontractorZoho(body);
    console.log("Zoho integration result:", zohoResult);

    // Convert files to email attachments
    const attachments = await convertFilesToAttachments(files);

    // Send notification email
    const emailResult = await subcontractorService.sendNotificationEmail({
      to: body.email,
      subcontractorData: body,
      attachments: attachments,
    });

    // Check if there was an error in the email service response
    if (emailResult && 'error' in emailResult) {
      console.warn("Email sending failed:", emailResult.error);
      // Continue processing even if email fails
    }

    // Success response
    return new Response(
      JSON.stringify({
        data: {
          message: "Subcontractor created successfully",
          zoho: zohoResult,
          email: emailResult,
        },
      }),
      {
        status: 201,
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error in create-subcontractor function:", error);

    return new Response(
      JSON.stringify({
        error: {
          message: "Internal server error",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

// @ts-ignore: Deno is available in Supabase Edge Functions
Deno.serve(async (request: Request) => {
  // Handle CORS preflight requests
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  // Only allow POST requests
  if (request.method !== "POST") {
    return new Response(
      JSON.stringify({
        error: {
          message: "Method not allowed. Only POST requests are supported.",
        },
      }),
      {
        status: 405,
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  const response = await handleCreateSubcontractor(request);

  // Add CORS headers to the response
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  return response;
});