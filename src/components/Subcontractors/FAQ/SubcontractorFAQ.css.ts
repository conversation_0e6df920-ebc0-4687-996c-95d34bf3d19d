import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const section = style({
  padding: "0 0 60px",
  backgroundColor: theme.colors.primary.ivory,

  "@media": {
    [breakpoints.tablet]: {
      padding: "60px 0 80px",
    },
    [breakpoints.desktop]: {
      padding: "80px 0 100px",
    }
  }
});

export const container = style({
  display: "flex",
  flexDirection: "column",
  maxWidth: "1200px",
  margin: "0 auto",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      gap: "40px",
    },
    [breakpoints.desktop]: {
      gap: "60px",
    }
  }
});

export const header = style({
  marginBottom: "30px",

  "@media": {
    [breakpoints.tablet]: {
      width: "30%",
      marginBottom: 0,
    }
  }
});

export const title = style({
  color: theme.colors.primary.castletonGreen,
  fontSize: "32px",
  fontFamily: "'Playfair Display', serif",
  marginBottom: "16px",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "36px",
      textAlign: "left",
    },
    [breakpoints.desktop]: {
      fontSize: "80px",
    }
  }
});

export const subtitle = style({
  color: theme.colors.primary.castletonGreen,
  maxWidth: "345px",
  fontSize: "20px",
  lineHeight: 1.2,
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      textAlign: "left",
    }
  }
});

export const faqList = style({
  display: "flex",
  flexDirection: "column",
  gap: "8px",

  "@media": {
    [breakpoints.tablet]: {
      width: "70%",
    }
  }
});

export const faqItem = style({
  backgroundColor: theme.colors.primary.softWhite,
  borderRadius: "8px",
  overflow: "hidden",
  transition: "all 0.3s ease",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
  marginBottom: "4px",

  selectors: {
    '&[data-open="true"]': {
      boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    }
  }
});

export const questionButton = style({
  width: "100%",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: "16px 20px",
  background: "none",
  border: "none",
  cursor: "pointer",
  textAlign: "left",
});

export const question = style({
  color: theme.colors.primary.castletonGreen,
  fontWeight: 500,
  lineHeight: "120%",
  letterSpacing: "-0.24px",
  flex: 1,
  fontSize: "18px",
  fontFamily: theme.fonts.secondary,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "24px",
    }
  }

});

export const answer = style({
  padding: "0 20px 20px",
  color: theme.colors.primary.castletonGreen,
  lineHeight: 1.2,
  fontSize: "14px",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "18px"
    }
  }
});
