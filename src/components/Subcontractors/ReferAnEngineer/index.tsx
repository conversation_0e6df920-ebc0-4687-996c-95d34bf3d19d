"use client"

import React, {useState} from 'react';
import * as styles from './ReferAnEngineer.css';
import referalImg from './referal.png';
import Container from "@components/Container";
import Image from "next/image";
import unlimitedEarning from "./unlimited_earning_potential.svg";
import howItWorksIcon from "./how_it_works.svg"
import arrowUp from "@components/Subcontractors/FAQ/arrow-up.svg";
import arrowDown from "@components/Subcontractors/FAQ/arrow-down.svg";
import {highlightedParagraph, titleDropDownIcon} from "./ReferAnEngineer.css";

const ReferAnEngineer: React.FC = () => {
  const [openMenu, setOpenMenu] = useState<boolean>(false);

  return (
    <Container>
      <section className={styles.container}>
        <div className={styles.leftPanel}>
          <h2 className={styles.title}>
            Refer an Engineer<br />
            and <span className={styles.earn}>Earn</span><br />
            <span className={styles.cash}>£1,000 Cash</span>
          </h2>
          <p className={styles.subtitle}>Got a mate who's a great engineer?</p>
          <p className={styles.description}>
            Refer them to Pleasant Plumbers — and earn up to £1,000 when they join and get to work.
          </p>
          <img src={referalImg.src} alt="referral image" className={styles.image} />
        </div>
        <div className={styles.rightPanel}>
          <div className={styles.accordion}>
            <div className={styles.accordionItem}>
              <h3 className={styles.titleWithDropdown} onClick={() => setOpenMenu(!openMenu)}>
                <span>Here's the Breakdown:</span>
                {openMenu
                    ? <Image className={styles.titleDropDownIcon} src={arrowUp.src} width={30} height={30} alt="arrow up" />
                    : <Image className={styles.titleDropDownIcon} src={arrowDown.src} width={30} height={30} alt="arrow down" />
                }
              </h3>
              {openMenu && (
                  <div className={styles.accordionContent}>
                    <ul className={styles.list}>
                      <li className={styles.li}>
                        <div className={styles.checkIcon}>✔</div>
                        <div>
                          <strong>£250 bonus</strong>
                          <div>once your referral completes 10 hours of work</div>
                        </div>
                      </li>
                      <li className={styles.li}>
                        <div className={styles.checkIcon}>✔</div>
                        <div>
                          <strong>£250 bonus</strong>
                          <div>when they hit 25 hours</div>
                        </div>
                      </li>
                      <li className={styles.li}>
                        <div className={styles.checkIcon}>✔</div>
                        <div>
                          <strong>£500 final bonus</strong>
                          <div>when they reach 50 hours completed</div>
                        </div>
                      </li>
                    </ul>
                    <p className={styles.total}>That's £1,000 total — paid directly to your bank account in easy stages, as your referral delivers real work.</p>
                  </div>
              )}
            </div>
            <div className={styles.accordionItem}>
              <h3 className={styles.accordionTitle}>
                <Image src={unlimitedEarning.src} className={styles.icon} width={24} height={24} alt='Unlimited Earning icon' />
                <span>Unlimited Earning Potential</span>
              </h3>
              <div className={styles.accordionContent}>
                <p>There's no cap on how many engineers you can refer. The more quality people you bring in — the more you earn.</p>
              </div>
            </div>
            <div className={styles.accordionItem}>
              <h3 className={styles.accordionTitle}>
                <Image src={howItWorksIcon.src} width={24} height={24} alt="how it works icon" />
                <span>How Does It Work?</span>
              </h3>
              <div className={styles.accordionContent}>
                <p>On the engineer onboarding form, there's a box that asks: </p>
                <p className={styles.highlightedParagraph}>"Were you referred by someone?"</p>
                <p>Just make sure your referral enters your full name in that box. That's it.</p>
                <p>We'll handle the rest — and you'll be paid as soon as they hit each milestone.</p>
              </div>
            </div>
          </div>
          <div className={styles.formContainer}>
            <p>Enter your email address below. We'll send you an info pack about working with us – simply forward it to anyone you'd like to refer.</p>
            <form className={styles.form}>
              <label htmlFor="email" className={styles.label}>Email</label>
              <div className={styles.inputGroup}>
                <input type="email" id="email" placeholder="<EMAIL>" className={styles.input} />
                <button type="submit" className={styles.button}>→</button>
              </div>
            </form>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default ReferAnEngineer;
