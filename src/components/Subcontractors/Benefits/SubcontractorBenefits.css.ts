import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style, styleVariants } from "@vanilla-extract/css";

export const section = style({
  padding: "0 0 60px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",

  "@media": {
    [breakpoints.tablet]: {
      padding: "20px 0 80px",
    },
    [breakpoints.desktop]: {
      padding: "20px 0 100px",
    }
  }
});

export const title = style({
  fontSize: "32px",
  fontWeight: 400,
  marginBottom: "40px",
  textAlign: "center",
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.primary,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "42px",
      marginBottom: "50px",
    },
    [breakpoints.desktop]: {
      fontSize: "64px",
      marginBottom: "60px",
    }
  }
});

export const titleHighlight = style({
  fontStyle: "italic",
  fontWeight: 600,
});

export const benefitsContainer = style({
  display: "flex",
  flexDirection: "column",
  gap: "24px",
  width: "100%",
  maxWidth: "1200px",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      gap: "30px",
    },
    [breakpoints.desktop]: {
      gap: "40px",
    }
  }
});

const benefitCardBase = style({
  borderRadius: "16px",
  padding: "40px 24px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  textAlign: "center",
  lineHeight: "95%",
  flex: 1,
  fontSize: "44px",
  letterSpacing: "-1.28px",
  maxWidth: "690px",
  minHeight: "368px",
  fontFamily: theme.fonts.primary,
  "@media": {
    [breakpoints.tablet]: {
      padding: "45px 32px",
      fontSize: "64px"
    },
    [breakpoints.desktop]: {
      // padding: "60px 40px",
    }
  }
});

export const benefitCard = styleVariants({
  green: [
    benefitCardBase,
    {
      backgroundColor: "#00E676",
      color: theme.colors.primary.castletonGreen,
    }
  ],
  dark: [
    benefitCardBase,
    {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: theme.colors.primary.ivory,
    }
  ]
});

export const iconWrapper = style({
  marginBottom: "24px",

  "@media": {
    [breakpoints.desktop]: {
      marginBottom: "32px",
    }
  }
});

const iconBase = style({
  width: "68px",
  height: "68px",
  borderRadius: "50%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "34px",
  fontWeight: "bold",

  "@media": {
    [breakpoints.desktop]: {
      width: "56px",
      height: "56px",
      fontSize: "28px",
    }
  }
});

export const icon = styleVariants({
  star: [
    iconBase,
    {
      backgroundColor: theme.colors.primary.castletonGreen,
      color: "#00E676",
    }
  ],
  pound: [
    iconBase,
    {
      backgroundColor: theme.colors.primary.ivory,
      color: theme.colors.primary.castletonGreen,
    }
  ]
});

export const  benefitTitle = style({
  fontSize: "44px",
  fontWeight: 400,
  marginBottom: "38px",
  fontFamily: theme.fonts.primary,
  lineHeight: "95%",

  "@media": {
    [breakpoints.desktop]: {
      fontSize: "64px",
    }
  }
});

export const benefitTitleHighlight = style({
  fontStyle: "italic",
  fontFamily: theme.fonts.primary,
  fontWeight: 600,
  display: "block",

  "@media": {
    [breakpoints.tablet]: {
      marginTop: "4px",
    }
  }
});

export const benefitDescription = style({
  fontSize: "24px",
  fontWeight: 500,
  fontFamily: theme.fonts.secondary,
  textAlign: "center",
  lineHeight: "120%",
  letterSpacing: "-0.24px",

  "@media": {
    [breakpoints.desktop]: {
      fontSize: "24px",
    }
  }
});
