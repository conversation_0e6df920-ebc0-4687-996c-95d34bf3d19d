// supabase/functions/create-subcontractor/services/create-subcontractor-zoho.ts

type SubcontractorData = {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  addressLine1: string;
  addressLine2?: string;
  postCode: string;
  dateOfBirth: string;
  gasRegistered: boolean;
  yearsExperience: number;
  travelDistance: number;
  hasOwnVan: boolean;
  hasOwnTools: boolean;
  workType: string;
  centralLondon: boolean;
  drivingLicense: boolean;
  publicLiabilityInsurance: boolean;
  availableDays?: string[];
  acceptedRates: boolean;
  outOfHoursWork: boolean;
  emergencyCallouts: boolean;
  preferredWorkType: string;
  additionalQualifications?: string;
};

type ZohoResponse = {
  success: boolean;
  data?: any;
  error?: string;
  recordId?: string;
};

/**
 * Mock function to create subcontractor in Zoho CRM
 * This is a placeholder implementation that simulates the Zoho API integration
 */
export async function createSubcontractorZoho(subcontractorData: SubcontractorData): Promise<ZohoResponse> {
  try {
    console.log("Creating subcontractor in Zoho CRM:", subcontractorData);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Format data for Zoho CRM (similar to the existing send-leads-to-zoho function)
    const zohoData = formatSubcontractorForZoho(subcontractorData);
    
    // Mock successful response
    const mockResponse: ZohoResponse = {
      success: true,
      recordId: `ZOHO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      data: {
        message: "Subcontractor record created successfully in Zoho CRM",
        details: {
          id: `ZOHO_${Date.now()}`,
          created_time: new Date().toISOString(),
          modified_time: new Date().toISOString(),
          created_by: {
            name: "System",
            id: "system_user"
          },
          module: "Leads",
          status: "Created"
        },
        zohoData: zohoData
      }
    };

    console.log("Mock Zoho response:", mockResponse);
    return mockResponse;

  } catch (error) {
    console.error("Error in createSubcontractorZoho:", error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create subcontractor in Zoho CRM"
    };
  }
}

/**
 * Format subcontractor data for Zoho CRM
 * Based on the existing send-leads-to-zoho function structure
 */
function formatSubcontractorForZoho(formData: SubcontractorData) {
  return {
    data: [{
      First_Name: formData.firstName,
      Last_Name: formData.lastName,
      Email: formData.email,
      Mobile: formData.mobile,
      Street: formData.addressLine1,
      Street_2: formData.addressLine2 || "",
      Zip_Code: formData.postCode,
      Date_of_Birth: formData.dateOfBirth,
      Lead_Source: "Subcontractor Application",
      Lead_Status: "New Application",
      Description: generateZohoDescription(formData),
      // Custom fields for subcontractor-specific data
      Gas_Registered: formData.gasRegistered ? 'Yes' : 'No',
      Years_Experience: formData.yearsExperience,
      Travel_Distance: formData.travelDistance,
      Has_Own_Van: formData.hasOwnVan ? 'Yes' : 'No',
      Has_Own_Tools: formData.hasOwnTools ? 'Yes' : 'No',
      Work_Type: formData.workType,
      Central_London: formData.centralLondon ? 'Yes' : 'No',
      Driving_License: formData.drivingLicense ? 'Yes' : 'No',
      Public_Liability_Insurance: formData.publicLiabilityInsurance ? 'Yes' : 'No',
      Available_Days: formData.availableDays?.join(", ") || "",
      Accepted_Rates: formData.acceptedRates ? 'Yes' : 'No',
      Out_Of_Hours_Work: formData.outOfHoursWork ? 'Yes' : 'No',
      Emergency_Callouts: formData.emergencyCallouts ? 'Yes' : 'No',
      Preferred_Work_Type: formData.preferredWorkType,
      Additional_Qualifications: formData.additionalQualifications || ""
    }]
  };
}

/**
 * Generate description field for Zoho CRM
 */
function generateZohoDescription(formData: SubcontractorData): string {
  return `
Subcontractor Application Details:
================================

Personal Information:
- Name: ${formData.firstName} ${formData.lastName}
- Email: ${formData.email}
- Mobile: ${formData.mobile}
- Address: ${formData.addressLine1}${formData.addressLine2 ? ', ' + formData.addressLine2 : ''}
- Post Code: ${formData.postCode}
- Date of Birth: ${formData.dateOfBirth}

Professional Details:
- Gas Registered: ${formData.gasRegistered ? 'Yes' : 'No'}
- Years Experience: ${formData.yearsExperience}
- Travel Distance: ${formData.travelDistance} miles
- Has Own Van: ${formData.hasOwnVan ? 'Yes' : 'No'}
- Has Own Tools: ${formData.hasOwnTools ? 'Yes' : 'No'}
- Work Type: ${formData.workType}
- Preferred Work Type: ${formData.preferredWorkType}

Location & Availability:
- Central London: ${formData.centralLondon ? 'Yes' : 'No'}
- Available Days: ${formData.availableDays?.join(", ") || "Not specified"}
- Out Of Hours Work: ${formData.outOfHoursWork ? 'Yes' : 'No'}
- Emergency Callouts: ${formData.emergencyCallouts ? 'Yes' : 'No'}

Compliance:
- Driving License: ${formData.drivingLicense ? 'Yes' : 'No'}
- Public Liability Insurance: ${formData.publicLiabilityInsurance ? 'Yes' : 'No'}
- Accepted Rates: ${formData.acceptedRates ? 'Yes' : 'No'}

Additional Information:
- Additional Qualifications: ${formData.additionalQualifications || "None specified"}

Application submitted on: ${new Date().toISOString()}
  `.trim();
}

/**
 * Mock function to update subcontractor status in Zoho
 */
export async function updateSubcontractorStatusZoho(recordId: string, status: string): Promise<ZohoResponse> {
  try {
    console.log(`Updating subcontractor status in Zoho CRM: ${recordId} -> ${status}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      success: true,
      recordId: recordId,
      data: {
        message: "Subcontractor status updated successfully in Zoho CRM",
        details: {
          id: recordId,
          status: status,
          modified_time: new Date().toISOString()
        }
      }
    };

  } catch (error) {
    console.error("Error updating subcontractor status in Zoho:", error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update subcontractor status in Zoho CRM"
    };
  }
}

/**
 * Mock function to search for existing subcontractor in Zoho
 */
export async function searchSubcontractorZoho(email: string): Promise<ZohoResponse> {
  try {
    console.log(`Searching for subcontractor in Zoho CRM: ${email}`);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock: randomly return existing or not found
    const exists = Math.random() > 0.8; // 20% chance of existing record

    if (exists) {
      return {
        success: true,
        data: {
          message: "Existing subcontractor found",
          records: [{
            id: `EXISTING_${Date.now()}`,
            Email: email,
            Lead_Status: "Existing",
            created_time: new Date(Date.now() - 86400000).toISOString() // 1 day ago
          }]
        }
      };
    } else {
      return {
        success: true,
        data: {
          message: "No existing subcontractor found",
          records: []
        }
      };
    }

  } catch (error) {
    console.error("Error searching subcontractor in Zoho:", error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search subcontractor in Zoho CRM"
    };
  }
}
