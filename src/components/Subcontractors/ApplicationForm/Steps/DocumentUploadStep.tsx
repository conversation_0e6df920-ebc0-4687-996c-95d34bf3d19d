import React, { useState, useRef } from "react";
import * as styles from "./Steps.css";
import { FormData } from "../SubcontractorApplicationForm";
import Button from "@/components/Button";

interface DocumentUploadStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  onSubmit: () => void;
  onBack: () => void;
}

const DocumentUploadStep: React.FC<DocumentUploadStepProps> = ({
  formData,
  updateFormData,
  onSubmit,
  onBack
}) => {
  const [dragActive, setDragActive] = useState<{ [key: string]: boolean }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await onSubmit();
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (field: keyof FormData, e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      if (field === 'additionalDocuments') {
        const newFiles = Array.from(e.target.files);
        const currentFiles = formData.additionalDocuments || [];
        updateFormData({ additionalDocuments: [...currentFiles, ...newFiles] });
      } else if (e.target.files[0]) {
        updateFormData({ [field]: e.target.files[0] } as any);
      }
    }
  };

  const handleDrag = (field: string, e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(prev => ({ ...prev, [field]: true }));
    } else if (e.type === "dragleave") {
      setDragActive(prev => ({ ...prev, [field]: false }));
    }
  };

  const handleDrop = (field: keyof FormData, e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(prev => ({ ...prev, [field]: false }));
    
    if (e.dataTransfer.files) {
      if (field === 'additionalDocuments') {
        const newFiles = Array.from(e.dataTransfer.files);
        const currentFiles = formData.additionalDocuments || [];
        updateFormData({ additionalDocuments: [...currentFiles, ...newFiles] });
      } else if (e.dataTransfer.files[0]) {
        updateFormData({ [field]: e.dataTransfer.files[0] } as any);
      }
    }
  };

  const handleButtonClick = (field: string) => {
    if (inputRefs.current[field]) {
      inputRefs.current[field]?.click();
    }
  };

  const renderFileUpload = (
    field: keyof FormData,
    showLabel: boolean = false,
    multiple: boolean = false
  ) => {
    const files = formData[field] as File[] | File | null;
    const isDragActive = dragActive[field as string];

    const handleRemoveFile = (index: number) => {
      if (field === 'additionalDocuments') {
        const currentFiles = [...(formData.additionalDocuments || [])];
        currentFiles.splice(index, 1);
        updateFormData({ additionalDocuments: currentFiles });
      }
    };

    const handleRemoveSingleFile = () => {
      updateFormData({ [field]: null } as any);
    };

    return (
      <div 
        className={`${styles.fileDropArea} ${isDragActive ? styles.dragActive : ''}`}
        onDragEnter={(e) => handleDrag(field as string, e)}
        onDragLeave={(e) => handleDrag(field as string, e)}
        onDragOver={(e) => handleDrag(field as string, e)}
        onDrop={(e) => handleDrop(field, e)}
      >
        {multiple && Array.isArray(files) && files.length > 0 ? (
          <div>
            {files.map((file, index) => (
              <div key={index} className={styles.filePreview}>
                <span className={styles.fileName}>{file.name}</span>
                <button
                  type="button"
                  className={styles.removeFileButton}
                  onClick={() => handleRemoveFile(index)}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        ) : !multiple && files && !Array.isArray(files) ? (
          <div className={styles.filePreview}>
            <span className={styles.fileName}>{(files as File).name}</span>
            <button
              type="button"
              className={styles.removeFileButton}
              onClick={handleRemoveSingleFile}
            >
              ×
            </button>
          </div>
        ) : (
          <div className={styles.uploadContainer}>
            <div className={styles.dropTextContainer}>
              <div className={styles.dropText}>Drag and drop a file here</div>
              <div className={styles.orText}>or</div>
            </div>
            <button 
              type="button"
              className={styles.fileSelectButton}
              onClick={() => handleButtonClick(field as string)}
            >
              {multiple ? "Select files" : "Select file"}
            </button>
            <input
              ref={el => inputRefs.current[field as string] = el}
              type="file"
              multiple={multiple}
              className={styles.hiddenFileInput}
              onChange={(e) => handleFileChange(field, e)}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={styles.stepWrapper}>
      <div className={styles.stepHeader}>
        <h2 className={styles.stepTitle}>Upload Your Documents</h2>
        <p className={styles.stepDescription}>
          Upload the following so we can issue your ID card, process your profile, and get you started quickly.
        </p>
        <div className={styles.stepIndicator}>Step 3/4</div>
      </div>

      <form onSubmit={handleSubmit} >
        <div className={styles.documentSection} style={{ marginBottom: "30px" }}>
          <h3 className={styles.documentLabel}>
            <span className={styles.labelText}>Profile photo</span> (for ID card) – clear front-facing photo.
          </h3>
          <p className={styles.documentDescription}>
            Feel free to smile! Don&apos;t worry about the background, we will remove it before putting the photo on your ID badge.
          </p>
          {renderFileUpload("idPhoto")}
        </div>

        <div className={styles.documentSection}>
          <h3 className={styles.documentLabel}>Photos of your van</h3>
          <p className={styles.documentDescription}>
            (Please upload photos showing the front, back, left side, and right side of your van)
          </p>
          
          <div className={styles.subSection}>
            <h4 className={styles.subSectionLabel}>Front</h4>
            {renderFileUpload("vanFrontPhoto")}
          </div>
          
          <div className={styles.subSection}>
            <h4 className={styles.subSectionLabel}>Back</h4>
            {renderFileUpload("vanBackPhoto")}
          </div>
          
          <div className={styles.subSection}>
            <h4 className={styles.subSectionLabel}>Left Side</h4>
            {renderFileUpload("vanLeftPhoto")}
          </div>
          
          <div className={styles.subSection}>
            <h4 className={styles.subSectionLabel}>Right Side</h4>
            {renderFileUpload("vanRightPhoto")}
          </div>
        </div>
        
        <div className={styles.documentSection}>
          <h3 className={styles.documentLabel}>Gas Safe Card</h3>
          {renderFileUpload("gasSafeCard")}
        </div>
        
        <div className={styles.documentSection}>
          <h3 className={styles.documentLabel}>Proof of Public Liability Insurance (PDF or clear photo)</h3>
          {renderFileUpload("insuranceProof")}
        </div>
        
        <div className={styles.documentSection}>
          <h3 className={styles.documentLabel}>Any additional qualifications or cards <span className={styles.optionalText}>(optional)</span></h3>
          {renderFileUpload("additionalDocuments", false, true)}
        </div>

        <div className={styles.buttonGroup}>
          <Button
            type="button"
            variant="outlined"
            color="secondary"
            onClick={onBack}
            className={styles.backButton}
            disabled={isSubmitting}
          >
            ← Back
          </Button>
          <Button
            type="submit"
            className={styles.nextButton}
            isLoading={isSubmitting}
          >
            Submit Application →
          </Button>
        </div>
      </form>
    </div>
  );
};

export default DocumentUploadStep;
