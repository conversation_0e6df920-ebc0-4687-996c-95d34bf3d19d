import EmergenciesByCityPage from "@/components/EmergenciesByCityPage";
import LandingPage from "@/components/LandingPage";
import { slicesFetch } from "@/slices/fetch";
import { PageMode } from "@/types/common";
import { getSliceDataObject, returnMetaDataRobots } from "@/utils/helpers";
import { KeyTextField } from "@prismicio/client";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { EmergencyLocationsDocument, LandingPageDocument } from "prismicio-types";
import { createClient } from "../../prismicio";

interface PageProps {
  params: {
    uid: string
  },
  searchParams: { mode: PageMode }
}

async function getData (uid: string, searchParams?: Record<string,string>) {
  try {
    // const headersList = headers();
    // const referer = headersList.get("referer")!;
    // const request = new NextRequest(referer);

    const client = createClient();

    const landingPage = await client.getByUID(
      "landing_page", uid
    ).catch(() => undefined);
    const {results: [emergencyPage]} = await client.getByType(
      "emergency_locations"
    );

    const page = (landingPage ?? emergencyPage) as LandingPageDocument<string> | EmergencyLocationsDocument<string>;

    if(!searchParams) {return {
      props:{page}
    };

    }
    const headerQuery = await client.getByType(
      "header"
    );


    const footerQuery = await client.getByType(
      "footer"
    );

    const emergencyLocationsQuery = await client.getByType(
      "emergency_locations"
    );

    
    // const slicesData = await Promise.all(
    //   page.data.slices
    //     .map(slice => slicesFetch[slice.slice_type as keyof typeof slicesFetch]?.()
    //       ?.then((data) => ({ data, sliceType: slice.slice_type }))).filter((sl) => sl)
    // );

    const slicesData = await getSliceDataObject(page.data.slices, slicesFetch, searchParams);

    const headerAnchorItems: KeyTextField[] = page.data.slices.map(({primary}) => "header_anchor_name" in primary ? primary?.header_anchor_name : null);

    return {
      props: {
        header: headerQuery.results[0],
        footer: footerQuery.results[0],
        emergencyLocations: emergencyLocationsQuery.results[0].data.locations,
        page,
        headerAnchorItems,
        slicesData: slicesData
      },
    };
  } catch (error) {
    notFound();
  }
}

export async function generateMetadata(
  { params, searchParams }: PageProps
): Promise<Metadata> {
  const { props } = await getData(params.uid);

  if (props.page.type === "emergency_locations") {
    const { meta_description, meta_image, meta_title, no_follow, no_index } = props.page.data.locations.find(({ uuid }) => (params.uid === uuid)) || {};

    return {
      title: meta_title,
      description: meta_description,
      robots: returnMetaDataRobots({noFollow: no_follow || false, noIndex:  no_index || false}),
      openGraph: {
        title: String(meta_title),
        description: String(meta_description),
        images: [meta_image?.url ?? ""],
      }
    };
  }

  if(searchParams.mode === "commercial") {
    return {
      title: props.page.data.title_commercial,
      description: props.page.data.description_commercial,
      robots: returnMetaDataRobots({noFollow: props.page.data.no_follow_commercial, noIndex:  props.page.data.no_index_commercial}),
      openGraph: {
        title: String(props.page.data.title_commercial),
        description: String(props.page.data.description_commercial),
        images: [props.page.data.image_commercial?.url ?? ""],
      }
    };
  }

  return {
    title: props.page.data.title_residential,
    description: props.page.data.description_residential,
    robots: returnMetaDataRobots({noFollow: props.page.data.no_follow_residential, noIndex:  props.page.data.no_index_residential}),
    openGraph: {
      title: String(props.page.data.title_residential),
      description: String(props.page.data.description_residential),
      images: [props.page.data.image_residential?.url ?? ""],
    }
  };
}

export default async function Page(
  { params, searchParams }: PageProps
) {
  const { props } = await getData(
    params.uid, searchParams
  );

  if (props.page.type === "emergency_locations") {
    return (
      <EmergenciesByCityPage
        footer={props.footer!}
        header={props.header!}
        headerAnchorItems={props.headerAnchorItems ?? []}
        page={props.page}
        slicesData={props.slicesData!}
      />
    );
  }

  return (
    <LandingPage
      searchParams={searchParams}
      header={props.header!}
      footer={props.footer!}
      emergencyLocations={props.emergencyLocations!}
      page={props.page}
      slicesData={props.slicesData!}
    />
  );
}
