"use client";

import { UserRegisterData } from "@/stores/authStore";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { observer } from "mobx-react-lite";
import Image from "next/image";
import Link from "next/link";
import { FC, ReactNode } from "react";
import { Controller, useForm } from "react-hook-form";
import { PatternFormat } from "react-number-format";
import AuthLayout from "../AuthLayout";
import Button from "../Button";
import TextInput from "../TextInput";
import Typography from "../Typography";
import * as styles from "./RegisterPage.css";
import PreviewImage from "@/assets/images/sign-up-login-preview.webp";
import PaymentPreviewImage from "@/assets/images/payment-preview.webp";
import {
  EMAIL_CONTROLLER_RULES,
  EMAIL_INPUT_PROPS,
  FULL_NAME_CONTROLLER_RULES,
  FULL_NAME_INPUT_PROPS,
  PHONE_NUMBER_CONTROLLER_RULES,
  PHONE_NUMBER_INPUT_PROPS,
} from "@/utils/constants";
import classNames from "classnames";

export type RegisterFormValues = UserRegisterData;

interface Props {
  onSubmit: (data: RegisterFormValues) => void;
  content?: ReactNode;
  isPaymentType?: boolean;
}

const RegisterPage: FC<Props> = observer(
  ({ onSubmit, content, isPaymentType }) => {
    const form = useForm<RegisterFormValues>({
      mode: "onBlur",
      defaultValues: {
        full_name: "",
        mobile_number: "",
        email: "",
        member_id: "",
      },
    });

    return (
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <AuthLayout
          beforeContentTitle={content}
          title={
            isPaymentType ? (
              <>
                Your{" "}
                <strong>
                  <em>Safety,</em>
                </strong>{" "}
                Our{" "}
                <strong>
                  <em>Expertise</em>
                </strong>
              </>
            ) : (
              "Membership"
            )
          }
          description={
            isPaymentType
              ? undefined
              : "It's free and comes with an array of benefits including shorter wait times and discounted rates"
          }
          contentTitle={isPaymentType ? "Personal details" : "Become a Member"}
          content={
            <div className={gridSprinkle({ type: "grid" })}>
              <div className={gridSprinkle({ type: "item", cols: 10 })}>
                <Controller
                  rules={{
                    required: "Required!",
                    ...FULL_NAME_CONTROLLER_RULES,
                  }}
                  name="full_name"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <TextInput
                      {...FULL_NAME_INPUT_PROPS}
                      {...field}
                      error={fieldState.error?.message}
                      className={styles.field}
                      value={field.value
                        .replaceAll(/\s{2,}/g, " ")
                        .replaceAll(".", "")}
                    />
                  )}
                />
              </div>
              <div
                className={gridSprinkle({
                  type: "item",
                  cols: { mobile: 10, tablet: 5 },
                })}
              >
                <Controller
                  rules={{
                    required: "Required!",
                    ...EMAIL_CONTROLLER_RULES,
                  }}
                  name="email"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <TextInput
                      {...EMAIL_INPUT_PROPS}
                      {...field}
                      error={fieldState.error?.message}
                      className={styles.field}
                    />
                  )}
                />
              </div>
              {/*<div
              className={gridSprinkle({
                type: "item",
                cols: { mobile: 10, tablet: 5 },
              })}
            >
              <Controller
                rules={{
                  required: "Required!",
                  minLength: {
                    value: 8,
                    message: "Min characters to enter - 8",
                  },
                  validate: (value) => {
                    if (!value.match(/[A-Z]/))
                      return "Min one uppercase character";

                    return undefined;
                  },
                }}
                name="password"
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...field}
                    className={styles.field}
                    error={fieldState.error?.message}
                    placeholder="Enter your password"
                    label="Password"
                    type="password"
                  />
                )}
              />
            </div>*/}
              <div
                className={gridSprinkle({
                  type: "item",
                  cols: { mobile: 10, tablet: 5 },
                })}
              >
                <Controller
                  rules={{
                    required: "Required!",
                    ...PHONE_NUMBER_CONTROLLER_RULES,
                  }}
                  name="mobile_number"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <PatternFormat
                      {...PHONE_NUMBER_INPUT_PROPS}
                      inputMode="tel"
                      className={styles.field}
                      error={fieldState.error?.message}
                      {...field}
                      customInput={TextInput}
                    />
                  )}
                />
              </div>
              <div className={gridSprinkle({ type: "item", cols: 10 })}>
                <Controller
                  rules={
                    {
                      //required: "Required!",
                      //validate: (value) => {
                      //  if (value.split(" ").length < 2) return "Min two words";
                      //},
                    }
                  }
                  name="member_id"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <TextInput
                      {...field}
                      error={fieldState.error?.message}
                      className={classNames(
                        styles.field,
                        styles.removeMarginBottom
                      )}
                      placeholder="L93892K930"
                      label="Did someone refer you to us? Enter their Member ID here (Optional)"
                    />
                  )}
                />
              </div>
              {/*<div
              className={classNames(
                styles.field,
                gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } })
              )}
            >
              <Controller
                name="date_of_birth"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <DatePicker
                    {...field}
                    error={fieldState.error?.message}
                    label="Date of birth"
                    maxDate={new Date()}
                  />
                )}
              />
            </div>*/}
              {/*<div
              className={gridSprinkle({
                type: "item",
                cols: { mobile: 10, tablet: 5 },
              })}
            >
              <Controller
                name="post_code"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <TextInput
                    error={fieldState.error?.message}
                    {...field}
                    className={styles.field}
                    maxLength={7}
                    label="Post code"
                    placeholder="Enter your post code"
                  />
                )}
              />
            </div>*/}
              {/*<div
              className={gridSprinkle({
                type: "item",
                cols: { mobile: 10, tablet: 5 },
              })}
            >
              <Controller
                rules={{
                  required: "Required!",
                }}
                name="address_details"
                control={form.control}
                render={({ field, fieldState }) => (
                  <TextInput
                    {...field}
                    error={fieldState.error?.message}
                    className={styles.field}
                    placeholder="Apartment, suite, etc. "
                    label="Address details"
                  />
                )}
              />
            </div>*/}
              {/*<div
              className={gridSprinkle({ type: "item", cols: 10 })}
            >
              <Controller
                name="residential_status"
                control={form.control}
                rules={{
                  required: "Required!",
                }}
                render={({ field, fieldState }) => (
                  <DropdownInput
                    {...field}
                    onChange={(option) => {
                      field.onChange(option);
                      field.onBlur();
                    }}
                    error={fieldState.error?.message}
                    className={styles.field}
                    options={RESIDENTIAL_STATUSES_OPTIONS}
                    placeholder="Select your status"
                    label="Residential status"
                  />
                )}
              />
            </div>*/}
            </div>
          }
          contentActions={
            <>
              <Button
                isLoading={form.formState.isSubmitting}
                className={styles.submitButton}
                disabled={
                  !form.formState.isValid ||
                  form.formState.isLoading ||
                  form.formState.isSubmitting
                }
                type="submit"
              >
                {isPaymentType ? "Next" : "Become a member"}
              </Button>
              <Typography variant="note" className={styles.actionNote}>
                By clicking the {isPaymentType ? "”Next”" : "“Become a member”"}
                , you agree to our{" "}
                <Link target="_blank" href="/terms-of-use">
                  Terms of use
                </Link>{" "}
                and{" "}
                <Link target="_blank" href="/privacy-policy">
                  Privacy policy
                </Link>
              </Typography>
            </>
          }
          image={
            <Image
              src={isPaymentType ? PaymentPreviewImage.src : PreviewImage.src}
              fill
              alt="Preview"
            />
          }
          tooltip={
            <>
              <Typography as="span" variant="bodySmall">
                Already have account?
              </Typography>{" "}
              <Typography
                as={Link}
                href={isPaymentType ? "/login?redirectTo=plans" : "/login"}
                variant="buttonMedium"
              >
                Log in
              </Typography>
            </>
          }
        />
      </form>
    );
  }
);

export default RegisterPage;
