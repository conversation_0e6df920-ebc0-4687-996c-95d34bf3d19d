"use client";

import React, { useState, ReactNode, useEffect } from "react";
import * as styles from "./headerSection.css";
import Image from "next/image";
import logo from "./logo.png";
import mailIcon from "./mail.png";
import callIcon from "./call.png";
import OfferModal from "@components/OfferModal";
import { INFO_EMAIL } from "@/utils/constants";
import Link from "next/link";
import classNames from 'classnames';

interface HeaderSectionProps {
  buttonText?: string;
  buttonAction?: () => void;
  buttonComponent?: ReactNode;
  showModal?: boolean;
  modalComponent?: ReactNode;
}

const HeaderSection: React.FC<HeaderSectionProps> = ({
  buttonText = "Claim Offer",
  buttonAction,
  buttonComponent,
  showModal = true,
  modalComponent,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  // Handle button click based on props
  const handleButtonClick = () => {
    if (buttonAction) {
      buttonAction();
    } else if (showModal) {
      openModal();
    }
  };

  // Mobile header layout
  if (isMobile) {
    return (
      <section className={styles.section}>
        <div className={styles.mobileContainer}>
          {/* Logo */}
          <Link href="/" passHref>
            <Image
              src={logo.src}
              alt="Pleasant Plumbers Logo"
              width={80}
              height={47}
              className={styles.logo}
            />
          </Link>

          {/* Action Buttons */}
          <div className={styles.mobileActions}>
            {/* Email Icon */}
            <a href={`mailto:${INFO_EMAIL}`} className={styles.mobileIconButton}>
              <Image src={mailIcon.src} height={20} width={25} alt="Email us" />
            </a>
            
            {/* Phone Icon */}
            <a href="tel:08000461000" className={styles.mobileIconButton}>
              <Image src={callIcon.src} height={20} width={20} alt="Call us" />
            </a>
            
            {/* CTA Button */}
            {buttonComponent ? (
              React.cloneElement(buttonComponent as React.ReactElement, {
                className: classNames((buttonComponent as React.ReactElement).props?.className, styles.mobileCTA),
                onClick: handleButtonClick
              })
            ) : (
              <button className={styles.mobileCTA} onClick={handleButtonClick}>
                {buttonText}
              </button>
            )}
          </div>
        </div>

        {showModal && modalComponent ? (
          modalComponent
        ) : (
          showModal && (
            <OfferModal
              open={isModalOpen}
              onClose={closeModal}
              email={INFO_EMAIL}
            />
          )
        )}
      </section>
    );
  }

  // Original desktop layout - unchanged
  return (
    <section className={styles.section}>
      <div className={styles.container}>
        {/* MOBILE TOP: Phone + Email */}
        <div className={styles.topMobile}>
          <a href="tel:08000461000" className={styles.phoneLink}>
            <Image style={{ marginRight: "5px" }} src={callIcon.src} height={14} width={16} alt="call" />
            0800 046 1000
          </a>
          <a href="mailto:<EMAIL>" className={styles.contactLink}>
            <Image src={mailIcon.src} height={14} width={16} alt="mail" />
            <EMAIL>
          </a>
        </div>

        {/* ALWAYS VISIBLE BOTTOM */}
        <div className={styles.bottom}>
          <Link href="/" passHref>
            <Image
              src={logo.src}
              alt="Pleasant Plumbers Logo"
              width={120}
              height={50}
              className={styles.logo}
            />
          </Link>

          {/* Desktop only right side */}
          <div className={styles.rightDesktop}>
            <a href="mailto:<EMAIL>" className={styles.contactLink}>
              <Image src={mailIcon.src} height={14} width={16} alt="mail" />
              <EMAIL>
            </a>
            <a href="tel:08000461000" className={styles.phoneLink}>
              <Image src={callIcon.src} height={14} width={16} alt="call" />
              0800 046 1000
            </a>
          </div>

          {/* Button - either custom component or default button */}
          {buttonComponent ? (
            React.cloneElement(buttonComponent as React.ReactElement, {
              className: classNames((buttonComponent as React.ReactElement).props?.className, styles.ctaButton)
            })
          ) : (
            <button className={styles.ctaButton} onClick={handleButtonClick}>
              {buttonText}
            </button>
          )}
        </div>
      </div>

      {showModal && modalComponent ? (
        modalComponent
      ) : (
        showModal && (
          <OfferModal
            open={isModalOpen}
            onClose={closeModal}
            email={INFO_EMAIL}
          />
        )
      )}
    </section>
  );
};

export default HeaderSection;
